"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { chain } from "@/providers/web3/web3-provider";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { DelegationAPI } from "../api/delegation.api";
import {
  ICreateRentalFormData,
  ICreateRentalResponse,
} from "../types/delegation.types";

/**
 * Response interface for listing signature data
 */
interface IListChickenResponse {
  status: number;
  message: string;
  data: {
    chickenTokenId: number;
    rentalId: number;
    roninPrice: string;
    insurancePrice: string;
    rentalPeriod: number;
    ownerAddress: string;
    signature: string;
  };
}

/**
 * Hook for handling the complete chicken listing process
 * Follows the pattern from useRentChicken for blockchain interactions
 */
export const useListChickenForRent = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const queryClient = useQueryClient();

  const [isListing, setIsListing] = useState(false);

  // Get contract addresses from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  const chickenGenesisAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_genesis_address
    : ("" as Address);

  const chickenLegacyAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_legacy_address
    : ("" as Address);

  const chickenGenesisAbi = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_genesis_abi
    : undefined;

  const chickenLegacyAbi = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_legacy_abi
    : undefined;

  const chickenGenesisThreshold = blockchainQuery.isSuccess
    ? blockchainQuery.data?.chicken_genesis_threshold
    : 2222;

  /**
   * Execute the complete listing process:
   * 1. Call API to create rental and get signature
   * 2. Execute blockchain transaction
   * 3. Handle success/error states
   */
  const executeListChickenForRent = async (formData: ICreateRentalFormData) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot list chicken", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return;
      }

      if (!rentalAddress) {
        toast.error("Cannot list chicken", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return;
      }

      // Validate that this is not a direct delegation
      if (formData.isDirectDelegation) {
        throw new Error("Direct delegations don't require blockchain listing");
      }

      setIsListing(true);

      // Step 1: Create rental and get signature from API
      toast.info("Creating rental listing...", {
        description: "Getting signature from server",
        position: "top-center",
      });

      const response: ICreateRentalResponse =
        await DelegationAPI.createRental(formData);

      if (response.status !== 1 || !response.data) {
        throw new Error("Failed to create rental listing");
      }

      // The API response should contain the signature data for listing
      const listingData =
        response.data as unknown as IListChickenResponse["data"];

      if (!listingData.signature) {
        throw new Error("No signature received from server");
      }

      const {
        chickenTokenId,
        rentalId,
        roninPrice,
        insurancePrice,
        rentalPeriod,
        signature,
      } = listingData;

      // Convert prices from wei string to bigint
      const ethPriceInWei = BigInt(roninPrice);
      const insurancePriceInWei = BigInt(insurancePrice || "0");

      // Step 2: Approve chicken NFT for rental contract
      toast.info("Approving chicken NFT...", {
        description: "Allowing rental contract to manage your chicken",
        position: "top-center",
      });

      // Determine which chicken contract to use based on token ID
      const isLegacyChicken = chickenTokenId > chickenGenesisThreshold;
      const chickenContractAddress = isLegacyChicken
        ? chickenLegacyAddress
        : chickenGenesisAddress;
      const chickenContractAbi = isLegacyChicken
        ? chickenLegacyAbi
        : chickenGenesisAbi;

      if (!chickenContractAddress || !chickenContractAbi) {
        throw new Error("Chicken contract not configured");
      }

      // Check if rental contract is already approved
      const isApproved = await publicClient.readContract({
        address: chickenContractAddress,
        abi: chickenContractAbi,
        functionName: "isApprovedForAll",
        args: [address, rentalAddress],
      });

      // Approve rental contract if not already approved
      if (!isApproved) {
        const approvalHash = await walletClient.writeContract({
          address: chickenContractAddress,
          abi: chickenContractAbi,
          functionName: "setApprovalForAll",
          args: [rentalAddress, true],
          chain,
          account: address,
        });

        // Wait for approval confirmation
        toast.info("Confirming approval...", {
          description: "Waiting for approval confirmation",
          position: "top-center",
        });

        const approvalReceipt = await publicClient.waitForTransactionReceipt({
          hash: approvalHash,
          confirmations: 1,
        });

        if (approvalReceipt.status !== "success") {
          throw new Error("Chicken approval failed");
        }

        toast.success("Chicken approved!", {
          description: "Rental contract can now manage your chicken",
          position: "top-center",
        });
      }

      // Step 3: Simulate the contract call
      toast.info("Simulating transaction...", {
        description: "Checking transaction validity",
        position: "top-center",
      });

      console.log("Listing parameters:", {
        chickenId: BigInt(chickenTokenId),
        rentId: BigInt(rentalId),
        ethPrice: ethPriceInWei,
        insurancePrice: insurancePriceInWei,
        rentDuration: BigInt(rentalPeriod),
        signature: signature as `0x${string}`,
      });

      const simulateReq = await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "listChickenForRent",
        args: [
          BigInt(chickenTokenId),
          BigInt(rentalId),
          ethPriceInWei,
          insurancePriceInWei,
          BigInt(rentalPeriod),
          signature as `0x${string}`,
        ],
        chain,
        account: address,
      });

      // Step 4: Estimate gas for the transaction
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "listChickenForRent",
        args: [
          BigInt(chickenTokenId),
          BigInt(rentalId),
          ethPriceInWei,
          insurancePriceInWei,
          BigInt(rentalPeriod),
          signature as `0x${string}`,
        ],
        account: address,
      });

      // Step 5: Execute the transaction
      toast.info("Listing chicken...", {
        description: "Please confirm the transaction in your wallet",
        position: "top-center",
      });

      const hash = await walletClient.writeContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "listChickenForRent",
        args: [
          BigInt(chickenTokenId),
          BigInt(rentalId),
          ethPriceInWei,
          insurancePriceInWei,
          BigInt(rentalPeriod),
          signature as `0x${string}`,
        ],
        gas: gasEstimate + BigInt(50000), // Add buffer
        chain,
        account: address,
      });

      // Step 5: Wait for transaction confirmation
      toast.info("Confirming transaction...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
      });

      if (receipt.status === "success") {
        toast.success("Chicken listed successfully!", {
          description: "Your chicken is now available for rent",
          position: "top-center",
        });

        // Invalidate relevant queries to refresh data
        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: ["available-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["chickens"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
          queryClient.invalidateQueries({ queryKey: ["chickens", address] });
          queryClient.invalidateQueries({
            queryKey: ["chickenRentalStatuses"],
          });
        }, 500);

        return {
          success: true,
          hash,
          receipt,
          data: response.data,
        };
      } else {
        throw new Error("Transaction failed");
      }
    } catch (error) {
      console.error("List chicken failed:", error);

      let errorMessage = "Failed to list chicken";
      let errorDescription = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;

        // Handle specific error cases
        if (error.message.includes("User rejected")) {
          errorDescription = "Transaction was cancelled by user";
        } else if (error.message.includes("insufficient funds")) {
          errorDescription = "Insufficient funds for gas fees";
        } else if (error.message.includes("Chicken not found")) {
          errorDescription = "The selected chicken was not found";
        } else if (error.message.includes("already rented")) {
          errorDescription = "This chicken is already rented or delegated";
        } else if (error.message.includes("not owned")) {
          errorDescription = "You don't own this chicken";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error };
    } finally {
      setIsListing(false);
    }
  };

  // Mutation for React Query integration
  const listChickenMutation = useMutation({
    mutationFn: executeListChickenForRent,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("List chicken mutation error:", error);
    },
  });

  return {
    executeListChickenForRent,
    listChickenMutation,
    isListing,
    rentalAddress,
  };
};
